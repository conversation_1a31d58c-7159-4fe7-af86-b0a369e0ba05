-- Manual Database Cleanup Script
-- Run this in your Supabase SQL Editor to fix duplicate personality profiles

-- Step 1: Check current state of personality_profiles table
SELECT 
    user_id, 
    COUNT(*) as profile_count,
    STRING_AGG(id::text, ', ') as profile_ids,
    STRING_AGG(created_at::text, ', ') as created_dates
FROM personality_profiles 
GROUP BY user_id 
HAVING COUNT(*) > 1
ORDER BY profile_count DESC;

-- Step 2: Create a function to clean up duplicates (keeping the most recent)
CREATE OR REPLACE FUNCTION cleanup_duplicate_profiles() RETURNS void AS $$
DECLARE
    user_rec RECORD;
    profile_count INTEGER;
    profiles_to_keep UUID[];
    profiles_to_delete UUID[];
BEGIN
    -- Find all users with multiple profiles
    FOR user_rec IN 
        SELECT user_id 
        FROM personality_profiles 
        GROUP BY user_id 
        HAVING COUNT(*) > 1
    LOOP
        RAISE NOTICE 'Cleaning up profiles for user %', user_rec.user_id;
        
        -- Keep only the most recently created profile
        profiles_to_keep := ARRAY(
            SELECT id FROM personality_profiles
            WHERE user_id = user_rec.user_id
            ORDER BY created_at DESC NULLS LAST
            LIMIT 1
        );
        
        -- Get profiles to delete (all except the most recent)
        profiles_to_delete := ARRAY(
            SELECT id FROM personality_profiles
            WHERE user_id = user_rec.user_id
            AND id <> ALL(profiles_to_keep)
        );
        
        RAISE NOTICE 'Keeping profile % and deleting %', profiles_to_keep, profiles_to_delete;
        
        -- Delete the duplicate profiles
        DELETE FROM personality_profiles
        WHERE id = ANY(profiles_to_delete);
        
        -- Get the count after cleanup
        SELECT COUNT(*) INTO profile_count
        FROM personality_profiles
        WHERE user_id = user_rec.user_id;
        
        RAISE NOTICE 'After cleanup, user % has % profile(s)', user_rec.user_id, profile_count;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Step 3: Run the cleanup function
SELECT cleanup_duplicate_profiles();

-- Step 4: Verify no duplicates remain
SELECT 
    user_id, 
    COUNT(*) as profile_count
FROM personality_profiles 
GROUP BY user_id 
HAVING COUNT(*) > 1;

-- Step 5: Drop the cleanup function
DROP FUNCTION cleanup_duplicate_profiles();

-- Step 6: Now add the unique constraint (this should work now)
ALTER TABLE personality_profiles
DROP CONSTRAINT IF EXISTS personality_profiles_user_id_unique;

ALTER TABLE personality_profiles
ADD CONSTRAINT personality_profiles_user_id_unique UNIQUE (user_id);

-- Step 7: Verify the constraint was added successfully
SELECT 
    conname as constraint_name,
    contype as constraint_type
FROM pg_constraint 
WHERE conrelid = 'personality_profiles'::regclass 
AND conname = 'personality_profiles_user_id_unique';

-- Step 8: Final verification - check current state
SELECT 
    COUNT(*) as total_profiles,
    COUNT(DISTINCT user_id) as unique_users
FROM personality_profiles;

RAISE NOTICE 'Database cleanup completed successfully!';
