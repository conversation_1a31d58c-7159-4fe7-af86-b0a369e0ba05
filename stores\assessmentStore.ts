import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  supabase,
  isSupabaseConfigured,
  getDemoCredentials,
} from '../lib/supabase';
import { openRouterService } from '../lib/openrouter';
import { useAuthStore } from './authStore';
import { useProfileStore } from './profileStore';
import { router } from 'expo-router';
import { TemperamentType } from '../types/personality';

// Define the structure for a single assessment question
interface AssessmentQuestion {
  questionNumber: number;
  category: 'initial' | 'confirmation' | 'comparison' | 'tiebreaker';
  question: string;
  answers: string[];
  temperamentOrder: number[]; // Maps answer positions to temperament indices
}

// Define the structure for the entire assessment
interface AssessmentQuestions {
  instructions: string;
  questions: AssessmentQuestion[];
}

// Define the result structure
interface AssessmentResult {
  primaryTemperament: TemperamentType;
  secondaryTemperament: TemperamentType;
  primaryPercentage: number;
  secondaryPercentage: number;
  temperamentScores: {
    choleric: number;
    sanguine: number;
    melancholic: number;
    phlegmatic: number;
  };
  completedAt: string;
}

// Define the temperament compatibility map
// Each temperament can only be paired with adjacent temperaments
const TEMPERAMENT_COMPATIBILITY: Record<TemperamentType, TemperamentType[]> = {
  choleric: ['sanguine', 'melancholic'],
  sanguine: ['choleric', 'phlegmatic'],
  melancholic: ['choleric', 'phlegmatic'],
  phlegmatic: ['sanguine', 'melancholic'],
};

// Define the temperament mapping array for translating indices to types
const TEMPERAMENT_MAPPING = [
  'choleric',
  'sanguine',
  'melancholic',
  'phlegmatic',
] as const;

// Define the assessment store interface
interface AssessmentState {
  // State
  phase: 'initial' | 'confirmation' | 'comparison' | 'complete';
  questions: AssessmentQuestion[];
  currentQuestionIndex: number;
  responses: number[]; // Temperament indices selected
  selectedAnswer: number | null;
  intermediateScores: {
    choleric: number;
    sanguine: number;
    melancholic: number;
    phlegmatic: number;
  };
  topTemperaments: TemperamentType[];
  primaryTemperament: TemperamentType | null;
  compatibleTemperaments: TemperamentType[];
  result: AssessmentResult | null;
  isLoading: boolean;
  isGeneratingQuestions: boolean;
  isSubmitting: boolean;
  error: string | null;
  aiError: string | null;
  isRetake: boolean;
  useAI: boolean;
  isDemo: boolean;

  // Actions
  initializeAssessment: () => Promise<void>;
  initializeRetakeAssessment: () => Promise<void>;
  loadInitialQuestions: () => Promise<void>;
  generateConfirmationQuestions: () => Promise<void>;
  generateComparisonQuestions: () => Promise<void>;
  handleAnswerSelect: (answerIndex: number) => void;
  handleNextQuestion: () => Promise<void>;
  handlePreviousQuestion: () => void;
  calculateIntermediateScores: () => void;
  calculateResults: () => void;
  submitAssessment: () => Promise<void>;
  regenerateQuestions: () => Promise<void>;
  useFallbackQuestions: () => void;
  resetAssessment: () => void;
  clearError: () => void;
  getProgressPercentage: () => number;
  loadDemoAssessment: () => void;
}

export const useAssessmentStore = create<AssessmentState>()(
  persist(
    (set, get) => ({
      // Initial state
      phase: 'initial',
      questions: [],
      currentQuestionIndex: 0,
      responses: [],
      selectedAnswer: null,
      intermediateScores: {
        choleric: 0,
        sanguine: 0,
        melancholic: 0,
        phlegmatic: 0,
      },
      topTemperaments: [],
      primaryTemperament: null,
      compatibleTemperaments: [],
      result: null,
      isLoading: false,
      isGeneratingQuestions: false,
      isSubmitting: false,
      error: null,
      aiError: null,
      isRetake: false,
      useAI: true,
      isDemo: false,

      // Initialize the assessment
      initializeAssessment: async () => {
        try {
          set({
            isLoading: true,
            error: null,
            phase: 'initial',
            currentQuestionIndex: 0,
            responses: [],
            selectedAnswer: null,
            intermediateScores: {
              choleric: 0,
              sanguine: 0,
              melancholic: 0,
              phlegmatic: 0,
            },
            topTemperaments: [],
            primaryTemperament: null,
            compatibleTemperaments: [],
            result: null,
          });

          // Check if user is authenticated
          if (!isSupabaseConfigured() || useAuthStore.getState().isDemo) {
            get().loadDemoAssessment();
            return;
          }

          const {
            data: { user },
          } = await supabase.auth.getUser();
          if (!user) {
            set({
              error: 'User not authenticated',
              isLoading: false,
            });
            router.replace('/auth');
            return;
          }

          // Check if user already has a personality profile
          const { data: existingProfile } = await supabase
            .from('personality_profiles')
            .select('id')
            .eq('user_id', user.id)
            .order('created_at', { ascending: false })
            .limit(1);

          if (existingProfile && existingProfile.length > 0) {
            console.log(
              '✅ Existing personality profile found - skipping assessment'
            );

            // Ensure the user profile has the correct completion flag
            console.log('🔄 Updating has_completed_assessment flag...');
            await useProfileStore.getState().setAssessmentCompleted(true);

            set({ isRetake: false });
            router.replace('/(tabs)'); // Go directly to main app instead of splash
            return;
          }

          // Only load questions if no profile exists
          await get().loadInitialQuestions();

          set({ isLoading: false });
        } catch (error: any) {
          console.error('Error initializing assessment:', error);
          set({
            error: error.message || 'Failed to initialize assessment',
            isLoading: false,
          });
        }
      },

      // Initialize assessment for retake (sets isRetake flag and clears state)
      initializeRetakeAssessment: async () => {
        console.log('🔄 Initializing retake assessment...');

        try {
          set({
            isLoading: true,
            error: null,
            phase: 'initial',
            currentQuestionIndex: 0,
            responses: [],
            selectedAnswer: null,
            intermediateScores: {
              choleric: 0,
              sanguine: 0,
              melancholic: 0,
              phlegmatic: 0,
            },
            topTemperaments: [],
            primaryTemperament: null,
            compatibleTemperaments: [],
            result: null,
            isRetake: true, // This is the key difference
            useAI: true,
            aiError: null,
          });

          // Check if user is authenticated
          if (!isSupabaseConfigured() || useAuthStore.getState().isDemo) {
            get().loadDemoAssessment();
            return;
          }

          const {
            data: { user },
          } = await supabase.auth.getUser();
          if (!user) {
            set({
              error: 'User not authenticated',
              isLoading: false,
            });
            router.replace('/auth');
            return;
          }

          console.log('✅ Retake assessment initialized for user:', user.id);

          // Load questions for retake
          await get().loadInitialQuestions();

          set({ isLoading: false });
        } catch (error: any) {
          console.error('Error initializing retake assessment:', error);
          set({
            error: error.message || 'Failed to initialize retake assessment',
            isLoading: false,
          });
        }
      },

      // Load the initial questions (Phase 1: Initial)
      loadInitialQuestions: async () => {
        if (get().isGeneratingQuestions) return;

        set({
          isGeneratingQuestions: true,
          error: null,
          aiError: null,
          phase: 'initial',
        });

        try {
          let assessmentQuestions: AssessmentQuestions;

          if (get().useAI && openRouterService.isConfigured()) {
            console.log(
              '🤖 Generating fresh AI-powered assessment questions for initial phase...'
            );
            try {
              assessmentQuestions =
                await openRouterService.generateAssessmentQuestions();
              console.log(
                '✅ AI questions generated successfully with mixed temperament positions'
              );
              set({ aiError: null });

              // We only need the first 4 questions (initial phase)
              assessmentQuestions.questions = assessmentQuestions.questions
                .slice(0, 4)
                .map((q, idx) => ({
                  ...q,
                  questionNumber: idx + 1,
                  category: 'initial',
                }));
            } catch (aiError: any) {
              console.warn('⚠️ AI generation failed:', aiError.message);
              set({ aiError: aiError.message });
              console.log('🔄 Falling back to enhanced default questions');
              assessmentQuestions = openRouterService.getFallbackQuestions();

              // Only keep the initial phase questions
              assessmentQuestions.questions =
                assessmentQuestions.questions.slice(0, 4);

              set({ useAI: false }); // Disable AI for this session
            }
          } else {
            console.log(
              '📝 Using enhanced fallback questions (AI not configured or disabled)'
            );
            assessmentQuestions = openRouterService.getFallbackQuestions();

            // Only keep the initial phase questions
            assessmentQuestions.questions = assessmentQuestions.questions.slice(
              0,
              4
            );

            if (!openRouterService.isConfigured()) {
              set({ aiError: 'OpenRouter API key not configured' });
            }
          }

          set({
            questions: assessmentQuestions.questions,
            isGeneratingQuestions: false,
          });
        } catch (error: any) {
          console.error(
            '💥 Error loading initial assessment questions:',
            error
          );
          set({
            error: error.message || 'Failed to load assessment questions',
            isGeneratingQuestions: false,
          });
        }
      },

      // Generate questions for the confirmation phase (Phase 2)
      generateConfirmationQuestions: async () => {
        set({
          isGeneratingQuestions: true,
          error: null,
          phase: 'confirmation',
        });

        try {
          // Get the top two temperaments from intermediate scores
          const { topTemperaments } = get();

          if (topTemperaments.length < 2) {
            throw new Error(
              'Cannot generate confirmation questions: insufficient top temperaments'
            );
          }

          // Either generate via AI or use fallback
          let confirmationQuestions: AssessmentQuestion[] = [];

          if (get().useAI && openRouterService.isConfigured()) {
            // TODO: Implement custom AI prompt for confirmation questions
            // For now, use modified fallback questions
            const fallbackQuestions = openRouterService.getFallbackQuestions();

            // Get confirmation questions and modify them to focus on top temperaments
            confirmationQuestions = fallbackQuestions.questions
              .slice(4, 6)
              .map((q, idx) => {
                // Create a new temperament order that prioritizes the top temperaments
                // This is a simplified approach - ideally we'd generate questions specifically for these
                const top1Index = TEMPERAMENT_MAPPING.indexOf(
                  topTemperaments[0] as TemperamentType
                );
                const top2Index = TEMPERAMENT_MAPPING.indexOf(
                  topTemperaments[1] as TemperamentType
                );

                // Put the top temperaments first, then the others
                const others = [0, 1, 2, 3].filter(
                  (i) => i !== top1Index && i !== top2Index
                );
                const newOrder = [top1Index, top2Index, others[0], others[1]];

                return {
                  ...q,
                  questionNumber: idx + 5, // 5-6
                  category: 'confirmation' as const,
                  temperamentOrder: newOrder,
                };
              });
          } else {
            // Use fallback questions but modify them
            const fallbackQuestions = openRouterService.getFallbackQuestions();

            // Same modification as above
            confirmationQuestions = fallbackQuestions.questions
              .slice(4, 6)
              .map((q, idx) => {
                const top1Index = TEMPERAMENT_MAPPING.indexOf(
                  topTemperaments[0] as TemperamentType
                );
                const top2Index = TEMPERAMENT_MAPPING.indexOf(
                  topTemperaments[1] as TemperamentType
                );

                const others = [0, 1, 2, 3].filter(
                  (i) => i !== top1Index && i !== top2Index
                );
                const newOrder = [top1Index, top2Index, others[0], others[1]];

                return {
                  ...q,
                  questionNumber: idx + 5, // 5-6
                  category: 'confirmation' as const,
                  temperamentOrder: newOrder,
                };
              });
          }

          // Update the questions array with confirmation questions
          set((state) => ({
            questions: [...state.questions, ...confirmationQuestions],
            isGeneratingQuestions: false,
          }));
        } catch (error: any) {
          console.error('💥 Error generating confirmation questions:', error);
          set({
            error: error.message || 'Failed to generate confirmation questions',
            isGeneratingQuestions: false,
          });
        }
      },

      // Generate questions for the comparison phase (Phase 3)
      generateComparisonQuestions: async () => {
        set({
          isGeneratingQuestions: true,
          error: null,
          phase: 'comparison',
        });

        try {
          // Get the primary temperament and its compatible temperaments
          const { primaryTemperament, compatibleTemperaments } = get();

          if (!primaryTemperament || compatibleTemperaments.length < 2) {
            throw new Error(
              'Cannot generate comparison questions: insufficient compatible temperaments'
            );
          }

          // Either generate via AI or use fallback
          let comparisonQuestions: AssessmentQuestion[] = [];

          if (get().useAI && openRouterService.isConfigured()) {
            // TODO: Implement custom AI prompt for comparison questions
            // For now, use modified fallback questions
            const fallbackQuestions = openRouterService.getFallbackQuestions();

            // Get comparison questions and modify them for compatible temperaments
            comparisonQuestions = fallbackQuestions.questions
              .slice(6, 10)
              .map((q, idx) => {
                // Create focused questions that only compare primary temperament
                // with its compatible alternatives (NEVER showing incompatible options)
                const primaryIndex = TEMPERAMENT_MAPPING.indexOf(
                  primaryTemperament as TemperamentType
                );
                const compat1Index = TEMPERAMENT_MAPPING.indexOf(
                  compatibleTemperaments[0] as TemperamentType
                );
                const compat2Index = TEMPERAMENT_MAPPING.indexOf(
                  compatibleTemperaments[1] as TemperamentType
                );

                // Critical fix: In comparison phase, we ONLY show answers for primary and compatible temperaments
                // We'll create questions with just 3 options - the primary and the two compatible ones
                // We don't include the incompatible option at all to prevent incorrect pairings
                const newAnswers = [
                  q.answers[0], // Primary temperament answer
                  q.answers[1], // First compatible temperament answer
                  q.answers[2], // Second compatible temperament answer
                ];

                // Our temperament order now only includes the three valid options
                const newOrder = [primaryIndex, compat1Index, compat2Index];

                return {
                  ...q,
                  answers: newAnswers, // Critical: only 3 answers now
                  questionNumber: idx + 7, // 7-10
                  category:
                    idx === 3
                      ? ('tiebreaker' as const)
                      : ('comparison' as const),
                  temperamentOrder: newOrder, // Only 3 temperaments
                };
              });
          } else {
            // Use fallback questions but modify them
            const fallbackQuestions = openRouterService.getFallbackQuestions();

            // Same modification as above
            comparisonQuestions = fallbackQuestions.questions
              .slice(6, 10)
              .map((q, idx) => {
                const primaryIndex = TEMPERAMENT_MAPPING.indexOf(
                  primaryTemperament as TemperamentType
                );
                const compat1Index = TEMPERAMENT_MAPPING.indexOf(
                  compatibleTemperaments[0] as TemperamentType
                );
                const compat2Index = TEMPERAMENT_MAPPING.indexOf(
                  compatibleTemperaments[1] as TemperamentType
                );

                // Critical fix: Only include answers for valid temperament combinations
                const newAnswers = [
                  q.answers[0], // Primary temperament answer
                  q.answers[1], // First compatible temperament answer
                  q.answers[2], // Second compatible temperament answer
                ];

                // Temperament order with only the three valid options
                const newOrder = [primaryIndex, compat1Index, compat2Index];

                return {
                  ...q,
                  answers: newAnswers, // Critical: only 3 answers now
                  questionNumber: idx + 7, // 7-10
                  category:
                    idx === 3
                      ? ('tiebreaker' as const)
                      : ('comparison' as const),
                  temperamentOrder: newOrder, // Only 3 temperaments
                };
              });
          }

          // Update the questions array with comparison questions
          set((state) => ({
            questions: [...state.questions, ...comparisonQuestions],
            isGeneratingQuestions: false,
          }));
        } catch (error: any) {
          console.error('💥 Error generating comparison questions:', error);
          set({
            error: error.message || 'Failed to generate comparison questions',
            isGeneratingQuestions: false,
          });
        }
      },

      // Handle answer selection
      handleAnswerSelect: (answerIndex: number) => {
        // Ensure the selection is valid for the current phase
        const { phase, questions, currentQuestionIndex } = get();

        // In comparison phase, we might have fewer answers (3 instead of 4)
        if (phase === 'comparison') {
          const currentQuestion = questions[currentQuestionIndex];
          if (answerIndex >= currentQuestion.answers.length) {
            console.warn(
              `Invalid answer selection: ${answerIndex} for question with ${currentQuestion.answers.length} answers`
            );
            return;
          }
        }

        set({ selectedAnswer: answerIndex });
      },

      // Handle moving to the next question
      handleNextQuestion: async () => {
        const {
          currentQuestionIndex,
          questions,
          selectedAnswer,
          responses,
          phase,
        } = get();

        if (selectedAnswer === null || !questions[currentQuestionIndex]) return;

        // Map the selected answer position to the actual temperament index
        const currentQ = questions[currentQuestionIndex];

        // In comparison phase, we have a different mapping due to only having 3 options
        let temperamentIndex: number;
        if (phase === 'comparison') {
          // In comparison phase, we're only showing 3 temperaments, so need to handle differently
          if (
            currentQ.temperamentOrder &&
            selectedAnswer < currentQ.temperamentOrder.length
          ) {
            temperamentIndex = currentQ.temperamentOrder[selectedAnswer];
          } else {
            console.warn(
              'Invalid answer selection for comparison phase question'
            );
            return;
          }
        } else {
          // In initial and confirmation phases, handle normally
          temperamentIndex = currentQ.temperamentOrder
            ? currentQ.temperamentOrder[selectedAnswer]
            : selectedAnswer;
        }

        console.log(
          `📊 Q${
            currentQ.questionNumber
          }: Selected answer ${selectedAnswer} (${String.fromCharCode(
            65 + selectedAnswer
          )}) = ${TEMPERAMENT_MAPPING[temperamentIndex]} temperament`
        );

        // Update responses
        const newResponses = [...responses, temperamentIndex];

        set({
          responses: newResponses,
          selectedAnswer: null,
        });

        // If we're at the end of a phase, calculate scores and proceed to next phase
        if (phase === 'initial' && currentQuestionIndex === 3) {
          // End of initial phase - calculate intermediate scores and move to confirmation
          set({ currentQuestionIndex: currentQuestionIndex + 1 });
          get().calculateIntermediateScores();
          await get().generateConfirmationQuestions();
        } else if (phase === 'confirmation' && currentQuestionIndex === 5) {
          // End of confirmation phase - determine primary temperament and move to comparison
          set({ currentQuestionIndex: currentQuestionIndex + 1 });
          get().calculateIntermediateScores();

          // Determine primary temperament and compatible temperaments
          const { intermediateScores } = get();

          // Find primary temperament (highest score)
          const sortedTemperaments = Object.entries(intermediateScores)
            .sort(([, scoreA], [, scoreB]) => scoreB - scoreA)
            .map(([temp]) => temp as TemperamentType);

          const primaryTemp = sortedTemperaments[0];

          // Get compatible temperaments based on primary
          const compatibleTemps = TEMPERAMENT_COMPATIBILITY[primaryTemp];

          set({
            primaryTemperament: primaryTemp,
            compatibleTemperaments: compatibleTemps,
          });

          await get().generateComparisonQuestions();
        } else if (currentQuestionIndex === questions.length - 1) {
          // End of assessment - calculate final results and submit
          get().calculateResults();
          if (!get().result) {
            set({ error: 'Failed to calculate results' });
            return;
          }

          await get().submitAssessment();
        } else {
          // Just move to the next question within the current phase
          set({ currentQuestionIndex: currentQuestionIndex + 1 });
        }
      },

      // Handle moving to the previous question
      handlePreviousQuestion: () => {
        const { currentQuestionIndex, responses } = get();

        if (currentQuestionIndex === 0) return;

        // If moving back across phase boundaries, we need to handle that
        const newIndex = currentQuestionIndex - 1;

        // Update phase if needed
        if (newIndex < 4) {
          set({ phase: 'initial' });
        } else if (newIndex < 6) {
          set({ phase: 'confirmation' });
        } else {
          set({ phase: 'comparison' });
        }

        // Remove the last response
        const newResponses = responses.slice(0, -1);

        // Find the previous answer's position based on temperament mapping
        let previousAnswer = null;
        if (newResponses.length > 0) {
          const { questions } = get();
          const prevQ = questions[newIndex];
          const prevTemperamentIndex = newResponses[newIndex];

          if (prevQ.temperamentOrder) {
            previousAnswer =
              prevQ.temperamentOrder.indexOf(prevTemperamentIndex);
          } else {
            previousAnswer = prevTemperamentIndex;
          }
        }

        set({
          currentQuestionIndex: newIndex,
          responses: newResponses,
          selectedAnswer: previousAnswer,
        });
      },

      // Calculate intermediate scores after initial and confirmation phases
      calculateIntermediateScores: () => {
        const { responses } = get();

        // Reset scores
        const scores = {
          choleric: 0,
          sanguine: 0,
          melancholic: 0,
          phlegmatic: 0,
        };

        // Count scores for each temperament
        responses.forEach((temperamentIndex) => {
          const temperament = TEMPERAMENT_MAPPING[temperamentIndex];
          scores[temperament]++;
        });

        console.log('🧮 Intermediate scores:', scores);

        // Find the top temperaments
        const sortedTemperaments = Object.entries(scores)
          .sort(([, scoreA], [, scoreB]) => scoreB - scoreA)
          .map(([temp]) => temp as TemperamentType);

        console.log('🏆 Top temperaments:', sortedTemperaments);

        set({
          intermediateScores: scores,
          topTemperaments: sortedTemperaments.slice(0, 2),
        });
      },

      // Calculate final results
      calculateResults: () => {
        const { responses } = get();

        // Reset scores
        const scores = {
          choleric: 0,
          sanguine: 0,
          melancholic: 0,
          phlegmatic: 0,
        };

        // Count scores for each temperament
        responses.forEach((temperamentIndex) => {
          const temperament = TEMPERAMENT_MAPPING[temperamentIndex];
          scores[temperament]++;
        });

        const total = responses.length;
        const percentages = {
          choleric: Math.round((scores.choleric / total) * 100),
          sanguine: Math.round((scores.sanguine / total) * 100),
          melancholic: Math.round((scores.melancholic / total) * 100),
          phlegmatic: Math.round((scores.phlegmatic / total) * 100),
        };

        console.log('📈 Final scores:', {
          choleric: `${scores.choleric} (${percentages.choleric}%)`,
          sanguine: `${scores.sanguine} (${percentages.sanguine}%)`,
          melancholic: `${scores.melancholic} (${percentages.melancholic}%)`,
          phlegmatic: `${scores.phlegmatic} (${percentages.phlegmatic}%)`,
        });

        // Find dominant and secondary temperaments
        const sortedTemperaments = Object.entries(scores)
          .sort(([, scoreA], [, scoreB]) => scoreB - scoreA)
          .map(([temp]) => temp as TemperamentType);

        // Make sure the secondary temperament is compatible with primary
        const primaryTemperament = sortedTemperaments[0];
        let secondaryTemperament = sortedTemperaments[1];

        // If the top secondary is not compatible, find the highest compatible one
        if (
          !TEMPERAMENT_COMPATIBILITY[primaryTemperament].includes(
            secondaryTemperament
          )
        ) {
          console.log(
            '⚠️ Top secondary temperament is not compatible with primary, finding compatible alternative'
          );

          // Find the highest scoring compatible temperament
          for (const temp of sortedTemperaments.slice(1)) {
            if (TEMPERAMENT_COMPATIBILITY[primaryTemperament].includes(temp)) {
              secondaryTemperament = temp;
              break;
            }
          }
        }

        const result: AssessmentResult = {
          primaryTemperament,
          secondaryTemperament,
          primaryPercentage: percentages[primaryTemperament],
          secondaryPercentage: percentages[secondaryTemperament],
          temperamentScores: scores,
          completedAt: new Date().toISOString(),
        };

        console.log('🎯 Final personality result:', result);

        set({
          result,
          phase: 'complete',
        });
      },

      // Submit assessment results to database
      submitAssessment: async () => {
        set({
          isSubmitting: true,
          error: null,
        });

        try {
          // If in demo mode, just return success
          if (!isSupabaseConfigured() || useAuthStore.getState().isDemo) {
            console.log('Demo mode: Assessment submission simulated');

            // Update profile store with the results
            const { result } = get();
            if (result) {
              await useProfileStore.getState().updatePersonalityProfile({
                dominant_temperament: result.primaryTemperament,
                secondary_temperament: result.secondaryTemperament,
                dominant_percentage: result.primaryPercentage,
                secondary_percentage: result.secondaryPercentage,
              });
            }

            set({ isSubmitting: false });
            router.replace('/result');
            return;
          }

          const {
            data: { user },
          } = await supabase.auth.getUser();
          if (!user) {
            throw new Error('No user found');
          }

          console.log('📝 Submitting assessment for user:', user.id);
          console.log('🔄 Is retake:', get().isRetake);

          const { result, isRetake } = get();
          if (!result) {
            throw new Error('No assessment result to submit');
          }

          // If this is a retake, delete all old personality profiles first
          if (isRetake) {
            console.log(
              '🗑️ Retake detected - cleaning up old personality profiles...'
            );

            try {
              // First, delete assessment responses to avoid FK constraints
              console.log('🧹 Deleting old assessment responses...');
              await supabase
                .from('assessment_responses')
                .delete()
                .eq('user_id', user.id);

              // Then delete all old personality profiles
              console.log('🗑️ Deleting old personality profiles...');
              const { error: deleteError } = await supabase
                .from('personality_profiles')
                .delete()
                .eq('user_id', user.id);

              if (deleteError) {
                console.warn(
                  '⚠️ Warning: Could not delete old personality profiles:',
                  deleteError
                );
                // Try using the stored procedure as fallback
                console.log('🔄 Trying stored procedure fallback...');
                const { error: procError } = await supabase.rpc(
                  'force_delete_personality_profiles',
                  {
                    user_id_param: user.id,
                  }
                );

                if (procError) {
                  console.warn('⚠️ Stored procedure also failed:', procError);
                  // Continue anyway - we'll create a new profile regardless
                }
              }

              console.log('✅ Old assessment data cleaned up successfully');
            } catch (cleanupError: any) {
              console.warn('⚠️ Warning during cleanup:', cleanupError.message);
              // Continue with creating new profile even if cleanup partially failed
            }
          } else {
            // For new assessments, just clean up assessment responses
            console.log('🧹 Cleaning up assessment responses...');
            await supabase
              .from('assessment_responses')
              .delete()
              .eq('user_id', user.id);
          }

          console.log('💾 Creating new personality profile...');

          let profileData;

          // Always INSERT a new profile (multiple profiles allowed per user)
          console.log(
            '📝 Creating new personality profile (multiple allowed)...'
          );
          const { data, error } = await supabase
            .from('personality_profiles')
            .insert({
              user_id: user.id,
              dominant_temperament: result.primaryTemperament,
              secondary_temperament: result.secondaryTemperament,
              dominant_percentage: result.primaryPercentage,
              secondary_percentage: result.secondaryPercentage,
            })
            .select();

          if (error) {
            console.error('❌ Error saving personality profile:', error);
            throw new Error(
              `Failed to save personality profile: ${error.message}`
            );
          }

          // Get the first inserted profile (there should only be one in this insert)
          profileData = data?.[0];
          if (!profileData) {
            console.error('❌ No profile data returned from database');
            throw new Error(
              'Failed to create personality profile: No data returned'
            );
          }

          console.log(
            '✅ New personality profile saved successfully:',
            profileData.id
          );

          // Save individual assessment responses with the correct temperament mapping
          console.log('💾 Saving assessment responses...');
          const { responses } = get();
          const responseRecords = responses.map((temperamentIndex, index) => ({
            user_id: user.id,
            question_number: index + 1,
            selected_answer: temperamentIndex, // Store the actual temperament index
          }));

          const { error: responsesError } = await supabase
            .from('assessment_responses')
            .insert(responseRecords);

          if (responsesError) {
            console.warn(
              '⚠️ Warning: Could not save assessment responses:',
              responsesError
            );
            // This is not critical - the personality profile is the main data
          } else {
            console.log('✅ Assessment responses saved successfully');
          }

          // Update user profile to mark assessment as completed
          console.log('📝 Updating user profile completion status...');
          await useProfileStore.getState().setAssessmentCompleted(true);

          // Verify the new profile was created
          const { data: latestProfile } = await supabase
            .from('personality_profiles')
            .select('id, dominant_temperament, secondary_temperament')
            .eq('user_id', user.id)
            .order('created_at', { ascending: false })
            .limit(1)
            .single();

          if (latestProfile) {
            console.log(
              '✅ Latest profile:',
              `${latestProfile.dominant_temperament}/${latestProfile.secondary_temperament}`
            );
          } else {
            console.error('❌ No personality profile found after saving!');
          }

          console.log(
            '� Assessment completed successfully, redirecting to results'
          );
          set({ isSubmitting: false });
          router.replace('/result');
        } catch (error: any) {
          console.error('💥 Error submitting assessment:', error);
          set({
            error: error.message || 'Failed to submit assessment',
            isSubmitting: false,
          });
        }
      },

      // Regenerate all questions (primarily for testing)
      regenerateQuestions: async () => {
        console.log('🔄 Regenerating fresh AI questions...');
        set({
          currentQuestionIndex: 0,
          responses: [],
          selectedAnswer: null,
          questions: [],
          phase: 'initial',
          useAI: true,
          aiError: null,
        });

        await get().loadInitialQuestions();
      },

      // Switch to fallback questions
      useFallbackQuestions: () => {
        console.log('📝 Using fallback questions...');
        set({
          currentQuestionIndex: 0,
          responses: [],
          selectedAnswer: null,
          error: null,
          aiError: null,
          useAI: false,
          phase: 'initial',
        });

        const fallbackQuestions = openRouterService.getFallbackQuestions();

        // Only use the initial phase questions to start
        set({
          questions: fallbackQuestions.questions.slice(0, 4).map((q, idx) => ({
            ...q,
            questionNumber: idx + 1,
            category: 'initial' as const,
          })),
        });
      },

      // Reset the assessment to initial state
      resetAssessment: () => {
        set({
          phase: 'initial',
          questions: [],
          currentQuestionIndex: 0,
          responses: [],
          selectedAnswer: null,
          intermediateScores: {
            choleric: 0,
            sanguine: 0,
            melancholic: 0,
            phlegmatic: 0,
          },
          topTemperaments: [],
          primaryTemperament: null,
          compatibleTemperaments: [],
          result: null,
          error: null,
          aiError: null,
        });
      },

      // Clear any error messages
      clearError: () => {
        set({ error: null });
      },

      // Calculate progress percentage based on current phase and question
      getProgressPercentage: () => {
        const { currentQuestionIndex, questions, phase } = get();

        if (questions.length === 0) return 0;

        // Total questions is 10 (4 initial + 2 confirmation + 4 comparison)
        const totalQuestions = 10;

        // Calculate completed questions based on phase and index
        let completedQuestions = currentQuestionIndex;

        // Add phase-based offset
        if (phase === 'confirmation') {
          completedQuestions = 4 + currentQuestionIndex - 4; // 4 initial + current in confirmation
        } else if (phase === 'comparison') {
          completedQuestions = 6 + currentQuestionIndex - 6; // 4 initial + 2 confirmation + current in comparison
        } else if (phase === 'complete') {
          completedQuestions = totalQuestions;
        }

        return Math.round((completedQuestions / totalQuestions) * 100);
      },

      // Load a demo assessment for testing
      loadDemoAssessment: () => {
        console.log('🎮 Loading demo assessment...');

        // Load fallback questions
        const fallbackQuestions = openRouterService.getFallbackQuestions();

        // Only use the initial phase questions to start
        set({
          questions: fallbackQuestions.questions.slice(0, 4).map((q, idx) => ({
            ...q,
            questionNumber: idx + 1,
            category: 'initial' as const,
          })),
          isLoading: false,
          useAI: false,
          isDemo: true,
        });
      },
    }),
    {
      name: 'assessment-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        isRetake: state.isRetake,
      }),
    }
  )
);

// Initialize assessment state
export const initializeAssessment = async () => {
  const { initializeAssessment } = useAssessmentStore.getState();
  await initializeAssessment();
};
